// Mock logger để tránh lỗi
global.logger = {
  logInfo: () => {},
  logError: () => {}
};

const mongoose = require('mongoose');
const Report = require('./lib/models/report');
const JobType = require('./lib/models/jobType');

// Script để debug dữ liệu trong database
async function debugDatabase() {
  try {
    console.log('🔍 Debugging database data...');

    // 1. <PERSON>ểm tra JobTypes có chartTypes = 'heatmap'
    console.log('\n📋 JobTypes with heatmap chartType:');
    const heatmapJobTypes = await JobType.find({
      'quickReportTemplate.chartTypes': 'heatmap',
      status: 1,
      deletedAt: { $exists: false }
    }).select('name quickReportTemplate').lean();

    console.log(`Found ${heatmapJobTypes.length} JobTypes with heatmap chartType`);
    heatmapJobTypes.forEach((jt, index) => {
      console.log(`  ${index + 1}. ${jt.name}`);
      console.log(`     chartTypes: ${jt.quickReportTemplate?.chartTypes || []}`);
      console.log(`     metrics: ${Object.keys(jt.quickReportTemplate?.metrics || {})}`);

      // Kiểm tra metrics có needsDetails không
      if (jt.quickReportTemplate?.metrics) {
        Object.keys(jt.quickReportTemplate.metrics).forEach(key => {
          const metric = jt.quickReportTemplate.metrics[key];
          console.log(`       ${key}: needsDetails = ${metric.needsDetails || false}`);
        });
      }
    });

    // 2. Kiểm tra Reports trong 7 ngày gần đây
    console.log('\n📊 Recent Reports (last 7 days):');
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

    const recentReports = await Report.find({
      createdAt: { $gte: sevenDaysAgo },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' }
    })
    .populate('jobType', 'name quickReportTemplate')
    .lean()
    .limit(10);

    console.log(`Found ${recentReports.length} recent reports`);

    recentReports.forEach((report, index) => {
      console.log(`\n  ${index + 1}. Report ID: ${report._id}`);
      console.log(`     JobType: ${report.jobType?.name}`);
      console.log(`     Created: ${new Date(report.createdAt).toISOString()}`);
      console.log(`     Status: ${report.status}`);
      console.log(`     ReportType: ${report.reportType}`);

      // Metrics
      console.log(`     Metrics: ${JSON.stringify(report.metrics || {})}`);

      // JobType template
      if (report.jobType?.quickReportTemplate) {
        console.log(`     ChartTypes: ${report.jobType.quickReportTemplate.chartTypes || []}`);
        console.log(`     Template Metrics: ${Object.keys(report.jobType.quickReportTemplate.metrics || {})}`);
      }

      // Location areas
      if (report.details && report.details.length > 0) {
        report.details.forEach((detail, detailIndex) => {
          if (detail.location && detail.location.areas) {
            console.log(`     Detail ${detailIndex + 1} Areas: ${detail.location.areas.map(a => a.name).join(', ')}`);
          }
        });
      }

      // CreatedBy (không populate để tránh lỗi)
      console.log(`     CreatedBy ID: ${report.createdBy}`);
    });

    // 3. Kiểm tra tổng số Reports
    console.log('\n📈 Report Statistics:');
    const totalReports = await Report.countDocuments({
      deletedAt: { $exists: false },
      status: { $ne: 'draft' }
    });
    console.log(`Total reports: ${totalReports}`);

    const reportsWithMetrics = await Report.countDocuments({
      deletedAt: { $exists: false },
      status: { $ne: 'draft' },
      metrics: { $exists: true, $ne: {} }
    });
    console.log(`Reports with metrics: ${reportsWithMetrics}`);

    // 4. Kiểm tra Reports có jobType với heatmap chartType
    if (heatmapJobTypes.length > 0) {
      const heatmapJobTypeIds = heatmapJobTypes.map(jt => jt._id);
      const heatmapReports = await Report.countDocuments({
        jobType: { $in: heatmapJobTypeIds },
        deletedAt: { $exists: false },
        status: { $ne: 'draft' }
      });
      console.log(`Reports with heatmap JobTypes: ${heatmapReports}`);

      // Sample heatmap reports
      const sampleHeatmapReports = await Report.find({
        jobType: { $in: heatmapJobTypeIds },
        deletedAt: { $exists: false },
        status: { $ne: 'draft' }
      })
      .populate('jobType', 'name quickReportTemplate')
      .lean()
      .limit(3);

      console.log('\nSample heatmap reports:');
      sampleHeatmapReports.forEach((report, index) => {
        console.log(`  ${index + 1}. ${report.jobType?.name}: metrics = ${JSON.stringify(report.metrics || {})}`);
      });
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Chạy debug nếu file được execute trực tiếp
if (require.main === module) {
  debugDatabase()
    .then(() => {
      console.log('\n✅ Debug completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Debug failed:', error);
      process.exit(1);
    });
}

module.exports = { debugDatabase };
