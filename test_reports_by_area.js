// Mock logger để tránh lỗi
global.logger = {
  logInfo: () => {},
  logError: () => {}
};

const mongoose = require('mongoose');
const statisticsService = require('./lib/services/statisticsService');

// Test script để kiểm tra fix cho vấn đề totalIncidents = 0
async function testReportsByAreaStats() {
  try {
    console.log('🔍 Testing getReportsByAreaStats...');

    // Test với các tham số khác nhau
    const testCases = [
      {
        name: 'Test với timeRange = day',
        params: {
          timeRange: 'day',
          userId: 'test-user-id'
        }
      },
      {
        name: 'Test với timeRange = week',
        params: {
          timeRange: 'week',
          userId: 'test-user-id'
        }
      },
      {
        name: 'Test với custom range',
        params: {
          timeRange: 'custom',
          startDate: '01-01-2024',
          endDate: '31-12-2024',
          userId: 'test-user-id'
        }
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n📊 ${testCase.name}`);
      console.log('Params:', JSON.stringify(testCase.params, null, 2));

      const result = await statisticsService.getReportsByAreaStats(testCase.params);

      if (result.success) {
        console.log('✅ Success!');
        console.log('Summary:', {
          totalAreas: result.data?.summary?.totalAreas || 0,
          totalIncidents: result.data?.summary?.totalIncidents || 0,
          totalReports: result.data?.summary?.totalReports || 0,
          totalOfficers: result.data?.summary?.totalOfficers || 0
        });

        if (result.data?.areas && result.data.areas.length > 0) {
          console.log('Top 3 areas by incidents:');
          result.data.areas
            .sort((a, b) => b.summary.totalIncidents - a.summary.totalIncidents)
            .slice(0, 3)
            .forEach((area, index) => {
              console.log(`  ${index + 1}. ${area.areaName}: ${area.summary.totalIncidents} incidents, ${area.summary.totalReports} reports`);
            });
        }
      } else {
        console.log('❌ Failed:', result.message);
      }
    }

    // Test debug: Kiểm tra dữ liệu thô
    console.log('\n🔧 Debug: Checking raw data...');

    const StatisticsUtils = require('./lib/utils/statisticsUtils');
    const period = StatisticsUtils.getTimeRange('week');

    console.log('Period:', {
      startTimestamp: period.startTimestamp,
      endTimestamp: period.endTimestamp,
      startDate: new Date(period.startTimestamp).toISOString(),
      endDate: new Date(period.endTimestamp).toISOString()
    });

    // Test getReports method trực tiếp
    const reports = await statisticsService.getReports(period, 'heatmap');
    console.log(`Found ${reports.length} reports with heatmap chartType`);

    if (reports.length > 0) {
      console.log('Sample report structure:');
      const sampleReport = reports[0];
      console.log({
        id: sampleReport._id,
        jobType: sampleReport.jobType?.name,
        hasMetrics: !!sampleReport.metrics,
        metricsKeys: sampleReport.metrics ? Object.keys(sampleReport.metrics) : [],
        hasQuickReportTemplate: !!sampleReport.jobType?.quickReportTemplate,
        chartTypes: sampleReport.jobType?.quickReportTemplate?.chartTypes || [],
        templateMetrics: sampleReport.jobType?.quickReportTemplate?.metrics ?
          Object.keys(sampleReport.jobType.quickReportTemplate.metrics) : []
      });

      // Test calculateTotalIncidentsFromReports
      const totalIncidents = statisticsService.calculateTotalIncidentsFromReports(reports);
      console.log(`Total incidents calculated: ${totalIncidents}`);
    }

    // Test without chartType filter
    const allReports = await statisticsService.getReports(period);
    console.log(`Found ${allReports.length} total reports (no chartType filter)`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Chạy test nếu file được execute trực tiếp
if (require.main === module) {
  testReportsByAreaStats()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testReportsByAreaStats };
