const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

const ReportSchema = new mongoose.Schema(
  {
    // Thông tin cơ bản
    title: {
      type: String
    },
    description: {
      type: String
    },

    // Loại báo cáo: 'quick' (nhanh) hoặc 'detail' (chi tiết)
    reportType: {
      type: String,
      enum: ['quick', 'detail'],
      default: 'quick',
      required: true
    },

    // Liên kết với JobType (chính là loại báo cáo)
    jobType: {
      type: Schema.Types.ObjectId,
      ref: 'JobType',
      required: true
    },

    // Dữ liệu số liệu (chỉ lưu số lượng) - dùng cho báo cáo quick
    metrics: {
      type: Schema.Types.Mixed,
      default: {}
    },

    // Mã vụ việc - bắt buộc cho báo cáo detail
    caseCode: {
      type: String,
      sparse: true, // <PERSON> phép null/undefined, nhưng nếu có thì phải unique
      index: true
    },

    // Trạng thái công việc - dùng cho báo cáo detail
    workStatus: {
      type: String,
      enum: ['pending', 'in_progress', 'completed', 'cancelled', 'on_hold'],
      default: 'in_progress'
    },

    // Chi tiết báo cáo (cho các báo cáo cần thời gian/địa điểm chi tiết)
    // Với báo cáo detail: chỉ cho phép 1 phần tử
    // Với báo cáo quick: cho phép nhiều phần tử
    details: [{
      time: {
        type: Number // milliseconds timestamp
      },
      location: {
        address: String,
        coordinates: {
          type: [Number], // GeoJSON format: [lng, lat]
          index: '2dsphere'
        },
        // Giữ lại format cũ để tương thích
        lat: Number,
        lng: Number,
        areas: [{
          type: Schema.Types.ObjectId,
          ref: 'Area'
        }]
      }
    }],

    // Người tạo báo cáo
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Đơn vị
    units: [{
      type: Schema.Types.ObjectId,
      ref: 'Unit'
    }],

    // Trạng thái
    status: {
      type: String,
      enum: ['draft', 'submitted', 'approved', 'rejected'],
      default: 'draft'
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    deletedAt: {
      type: Number
    },
    summary:[{
      area:{
        type: Schema.Types.ObjectId,
        ref: 'Area'
      },
      count: {
        type: Number,
        default: 0
      }
    }]
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
ReportSchema.index({ jobType: 1, createdAt: 1 })
ReportSchema.index({ 'details.location.coordinates': '2dsphere' })
ReportSchema.index({ createdBy: 1, createdAt: -1 })
ReportSchema.index({ unit: 1, createdAt: -1 })

// Indexes mới cho các trường mở rộng
ReportSchema.index({ reportType: 1, createdAt: -1 })
ReportSchema.index({ caseCode: 1 }, { sparse: true }) // Sparse index cho caseCode
ReportSchema.index({ workStatus: 1, createdAt: -1 })
ReportSchema.index({ reportType: 1, workStatus: 1 }) // Compound index cho báo cáo detail

module.exports = mongoConnections("master").model("Report", ReportSchema)