/**
 * Service tổng hợp trạng thái điểm danh
 * Quản lý tổng quan về tất cả các loại điểm danh (thường + đột xuất)
 */

const WorkSchedule = require('../models/workSchedule');
const AttendanceRecord = require('../models/attendanceRecord');
const SuddenAttendanceSession = require('../models/suddenAttendanceSession');
const SuddenAttendanceRecord = require('../models/suddenAttendanceRecord');
const DateUtils = require('../utils/dateUtils');
const CONSTANTS = require('../const');

class AttendanceOverviewService {
  /**
   * L<PERSON>y tổng quan trạng thái điểm danh cho cán bộ
   * @param {String} userId - ID cán bộ
   * @param {Object} filters - B<PERSON> lọc (date, startDate, endDate, period)
   * @returns {Object} Tổng quan trạng thái điểm danh
   */
  async getAttendanceStatusOverview(userId, filters = {}) {
    try {
      const {
        date,
        startDate,
        endDate,
        period = 'day' // day, week, month
      } = filters;

      // Xác định khoảng thời gian
      const dateRange = this.calculateDateRange(date, startDate, endDate, period);

      // Lấy dữ liệu song song
      const [regularAttendance, suddenAttendance] = await Promise.all([
        this.getRegularAttendanceData(userId, dateRange),
        this.getSuddenAttendanceData(userId, dateRange)
      ]);

      // Tổng hợp dữ liệu
      const overview = this.combineAttendanceData(regularAttendance, suddenAttendance, dateRange);

      return {
        success: true,
        data: overview
      };

    } catch (error) {
      console.error('Error getting attendance status overview:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Tính toán khoảng thời gian dựa trên filters
   */
  calculateDateRange(date, startDate, endDate, period) {
    if (startDate && endDate) {
      return { startDate, endDate };
    }

    if (date) {
      return { startDate: date, endDate: date };
    }

    // Mặc định theo period
    const today = new Date();
    const todayStr = DateUtils.convertYYYYMMDDtoDDMMYYYY(today.toISOString().split('T')[0]);

    switch (period) {
      case 'week':
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);

        return {
          startDate: DateUtils.convertYYYYMMDDtoDDMMYYYY(startOfWeek.toISOString().split('T')[0]),
          endDate: DateUtils.convertYYYYMMDDtoDDMMYYYY(endOfWeek.toISOString().split('T')[0])
        };

      case 'month':
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        return {
          startDate: DateUtils.convertYYYYMMDDtoDDMMYYYY(startOfMonth.toISOString().split('T')[0]),
          endDate: DateUtils.convertYYYYMMDDtoDDMMYYYY(endOfMonth.toISOString().split('T')[0])
        };

      default: // day
        return { startDate: todayStr, endDate: todayStr };
    }
  }

  /**
   * Lấy dữ liệu điểm danh thường
   */
  async getRegularAttendanceData(userId, dateRange) {
    const { startDate, endDate } = dateRange;

    // Lấy lịch làm việc
    const schedules = await WorkSchedule.find({
      user: userId,
      date: { $gte: startDate, $lte: endDate },
      status: 1
    }).lean();

    // Lấy bản ghi điểm danh
    const records = await AttendanceRecord.find({
      user: userId,
      date: { $gte: startDate, $lte: endDate }
    }).lean();

    // Map records theo date và shift
    const recordsMap = {};
    records.forEach(record => {
      const key = `${record.date}_${record.shift}`;
      recordsMap[key] = record;
    });

    // Tổng hợp dữ liệu
    const attendanceData = [];
    schedules.forEach(schedule => {
      schedule.shifts.forEach(shift => {
        const key = `${schedule.date}_${shift.type}`;
        const record = recordsMap[key];

        attendanceData.push({
          type: 'regular',
          date: schedule.date,
          shift: shift.type,
          startTime: DateUtils.convertTimeStringToTimestamp(shift.startTime),
          status: this.determineRegularAttendanceStatus(shift, record),
          checkinTime: record ? record.checkinTime : null,
          location: record ? record.location : null,
          isExempted: ['excused', 'business_trip'].includes(shift.status),
          exemptionReason: shift.status === 'business_trip' ? 'Công tác' :
                          shift.status === 'excused' ? 'Nghỉ phép' : null,
          scheduleId: schedule._id,
          recordId: record ? record._id : null
        });
      });
    });

    return attendanceData;
  }

  /**
   * Lấy dữ liệu điểm danh đột xuất
   */
  async getSuddenAttendanceData(userId, dateRange) {
    const { startDate, endDate } = dateRange;

    // Convert date range to timestamps
    const startTimestamp = new Date(DateUtils.convertDDMMYYYYtoYYYYMMDD(startDate)).getTime();
    const endTimestamp = new Date(DateUtils.convertDDMMYYYYtoYYYYMMDD(endDate)).getTime() + (24 * 60 * 60 * 1000) - 1;

    // Lấy các phiên chấm công đột xuất
    const sessions = await SuddenAttendanceSession.find({
      targetUsers: userId,
      startTime: { $gte: startTimestamp, $lte: endTimestamp }
    }).lean();

    // Lấy bản ghi chấm công đột xuất
    const sessionIds = sessions.map(s => s._id);
    const records = await SuddenAttendanceRecord.find({
      session: { $in: sessionIds },
      user: userId
    }).lean();

    // Map records theo session
    const recordsMap = {};
    records.forEach(record => {
      recordsMap[record.session.toString()] = record;
    });

    // Tổng hợp dữ liệu
    const attendanceData = [];
    sessions.forEach(session => {
      const record = recordsMap[session._id.toString()];
      const sessionDate = new Date(session.startTime);
      const dateString = DateUtils.convertYYYYMMDDtoDDMMYYYY(sessionDate.toISOString().split('T')[0]);

      // Kiểm tra xem user có được miễn điểm danh không
      const exemption = session.exemptedUsers.find(ex => ex.user.toString() === userId);

      attendanceData.push({
        type: 'sudden',
        date: dateString,
        title: session.title,
        startTime: session.startTime,
        validTime: session.startTime + (session.validDurationMinutes * 60 * 1000),
        status: this.determineSuddenAttendanceStatus(session, record, exemption),
        checkinTime: record ? record.checkinTime : null,
        location: record ? record.location : null,
        isExempted: !!exemption,
        exemptionReason: exemption ? (exemption.reason === 'business_trip' ? 'Công tác' : 'Nghỉ phép') : null,
        scheduleId: session._id,
        recordId: record ? record._id : null
      });
    });

    return attendanceData;
  }

  /**
   * Xác định trạng thái điểm danh thường
   */
  determineRegularAttendanceStatus(shift, record) {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // Lấy thời gian checkin từ constants
    const checkinStartTime = shift.type === 'morning'
      ? CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_START
      : CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_START;

    const checkinEndTime = shift.type === 'morning'
      ? CONSTANTS.ATTENDANCE.CHECKIN_TIME.MORNING_CHECKIN_END
      : CONSTANTS.ATTENDANCE.CHECKIN_TIME.AFTERNOON_CHECKIN_END;

    // Nếu trạng thái là 'scheduled' và trong thời gian checkin và chưa có bản ghi điểm danh
    if (shift.status === CONSTANTS.ATTENDANCE.WORK_SCHEDULE_STATUS.SCHEDULED &&
        currentTimeInMinutes >= checkinStartTime &&
        currentTimeInMinutes <= checkinEndTime &&
        !record) {
      return CONSTANTS.ATTENDANCE.WORK_SCHEDULE_STATUS.NONATTENDANCE;
    }

    return shift.status;
  }

  /**
   * Xác định trạng thái điểm danh đột xuất
   */
  determineSuddenAttendanceStatus(session, record, exemption) {
    if (exemption) {
      return exemption.reason;
    }

    if (!record) {
      const now = Date.now();

      if (now >= session.startTime) {
        return 'nonattendance';
      } else {
        return 'scheduled';
      }
    }

    return record.status; // 'on_time' hoặc 'late'
  }

  /**
   * Tổng hợp dữ liệu điểm danh
   */
  combineAttendanceData(regularData, suddenData, dateRange) {
    const allAttendance = [...regularData, ...suddenData];

    // Sắp xếp theo ngày và thời gian
    allAttendance.sort((a, b) => {
      const dateCompare = a.date.localeCompare(b.date);
      if (dateCompare !== 0) return dateCompare;

      // So sánh thời gian
      const timeA = a.startTime;
      const timeB = b.startTime;
      return timeA - timeB;
    });

    // Tính thống kê
    // const statistics = this.calculateStatistics(allAttendance);

    return {
      dateRange,
      attendance: allAttendance,
      // statistics
    };
  }

  /**
   * Tính thống kê tổng quan
   */
  calculateStatistics(attendanceData) {
    const stats = {
      total: attendanceData.length,
      regular: {
        total: 0,
        onTime: 0,
        late: 0,
        absent: 0,
        nonattendance: 0,
        excused: 0,
        businessTrip: 0
      },
      sudden: {
        total: 0,
        onTime: 0,
        late: 0,
        absent: 0,
        nonattendance: 0,
        excused: 0,
        businessTrip: 0
      },
      overall: {
        completed: 0,
        absent: 0,
        exempted: 0,
        nonattendance: 0
      }
    };

    attendanceData.forEach(item => {
      if (item.type === 'regular') {
        stats.regular.total++;
        switch (item.status) {
          case 'on_time': stats.regular.onTime++; stats.overall.completed++; break;
          case 'late': stats.regular.late++; stats.overall.completed++; break;
          case 'absent': stats.regular.absent++; stats.overall.absent++; break;
          case 'nonattendance': stats.regular.nonattendance++; stats.overall.nonattendance++; break;
          case 'excused': stats.regular.excused++; stats.overall.exempted++; break;
          case 'business_trip': stats.regular.businessTrip++; stats.overall.exempted++; break;
        }
      } else {
        stats.sudden.total++;
        switch (item.status) {
          case 'on_time': stats.sudden.onTime++; stats.overall.completed++; break;
          case 'late': stats.sudden.late++; stats.overall.completed++; break;
          case 'absent': stats.sudden.absent++; stats.overall.absent++; break;
          case 'nonattendance': stats.sudden.nonattendance++; stats.overall.nonattendance++; break;
          case 'excused': stats.sudden.excused++; stats.overall.exempted++; break;
          case 'business_trip': stats.sudden.businessTrip++; stats.overall.exempted++; break;
        }
      }
    });

    return stats;
  }
}

module.exports = new AttendanceOverviewService();
